{"name": "real-estate", "version": "1.0.1", "private": true, "dependencies": {"@chakra-ui/icons": "^1.1.5", "@chakra-ui/react": "1.8.8", "@chakra-ui/system": "^1.12.1", "@chakra-ui/theme-tools": "^1.3.6", "@emotion/cache": "^11.7.1", "@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/resource": "^6.1.8", "@fullcalendar/resource-timeline": "^6.1.8", "@fullcalendar/scrollgrid": "^6.1.8", "@hypertheme-editor/chakra-ui": "^0.1.5", "@react-pdf/renderer": "^3.4.4", "@reduxjs/toolkit": "^2.0.1", "@stripe/react-stripe-js": "^2.3.0", "@stripe/stripe-js": "^2.1.6", "apexcharts": "^4.0.0", "axios": "^1.4.0", "chakra-ui-autocomplete": "^1.4.5", "dayjs": "^1.11.10", "exceljs": "^4.4.0", "formik": "^2.4.2", "framer-motion": "^4.1.17", "fullcalendar": "^6.1.8", "html2pdf.js": "^0.10.2", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "papaparse": "^5.4.1", "react": "17.0.2", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^3.7.0", "react-custom-scrollbars-2": "^4.2.1", "react-datepicker": "^4.16.0", "react-dom": "17.0.2", "react-dropzone": "^12.0.4", "react-email-editor": "^1.7.11", "react-icons": "^4.3.1", "react-redux": "^7.2.9", "react-router-dom": "^6.14.1", "react-scripts": "^5.0.0", "react-signature-canvas": "^1.0.6", "react-table": "^7.7.0", "react-toastify": "^9.1.3", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "stylis-plugin-rtl": "2.0.2", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build", "sitemap": "babel-node ./sitemap-builder.js"}, "resolutions": {"react-error-overlay": "6.0.9"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "gh-pages": "^3.2.3"}}