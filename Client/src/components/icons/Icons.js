import { createIcon } from "@chakra-ui/icons";

export const AdobexdLogo = createIcon({
  displayName: "AdobexdLogo",
  viewBox: "0 0 24 24",
  path: (
    <g clipPath='url(#clip0)'>
      <path
        d='M4.042 0h12.916A3.539 3.539 0 0120.5 3.542v12.416a3.539 3.539 0 01-3.542 3.542H4.042A3.539 3.539 0 01.5 15.958V3.542A3.539 3.539 0 014.042 0z'
        fill='#470137'
      />
      <path
        d='M11.017 5.124l-2.5 4.125 2.666 4.375a.143.143 0 01.017.1c-.008.034-.042.009-.092.017H9.2c-.133 0-.225-.008-.283-.092-.175-.35-.359-.691-.534-1.041a33.608 33.608 0 00-.566-1.05c-.2-.359-.4-.717-.6-1.084H7.2c-.175.359-.367.717-.558 1.075-.192.359-.384.717-.567 1.067-.192.35-.383.708-.575 1.05-.033.083-.1.092-.192.092H3.475c-.033 0-.058.016-.058-.025a.116.116 0 01.016-.092l2.592-4.25L3.5 5.116c-.025-.033-.033-.067-.017-.083.017-.025.05-.034.084-.034h1.891c.042 0 .084.009.117.017a.236.236 0 01.083.075c.159.358.342.717.534 1.075.2.358.391.708.6 **********.383.7.558 1.059h.017c.175-.367.358-.725.541-1.075.184-.35.375-.7.567-1.05.192-.35.375-.709.558-1.05.009-.034.025-.067.05-.084a.159.159 0 01.109-.016h1.758a.077.077 0 01.091.058c.009.008-.008.042-.024.058zM14.866 13.917a4.144 4.144 0 01-1.791-.375 2.906 2.906 0 01-1.259-1.134c-.308-.508-.458-1.141-.458-1.9a3.423 3.423 0 011.783-3.05c.584-.325 1.284-.483 2.109-.483.041 0 .1 0 .175.008.075.009.158.009.258.017V4.367c0-.059.025-.092.083-.092h1.692c.042-.008.075.025.083.058v7.95c0 .15.009.317.017.5.017.175.025.342.033.484 0 .058-.025.108-.083.133a6.365 6.365 0 01-1.358.4 7.35 7.35 0 01-1.284.117zm.817-1.667V8.583c-.075-.016-.15-.033-.225-.041-.092-.009-.183-.017-.275-.017-.325 0-.65.067-.942.217-.283.141-.525.35-.708.616-.183.267-.275.625-.275 1.059-.008.291.042.583.142.858.083.225.208.425.375.592.158.15.35.266.566.333.225.075.459.108.692.108.125 0 .242-.008.35-.016a.73.73 0 00.3-.042z'
        fill='#FF61F6'
      />
    </g>
  ),
});
export const AppleLogo = createIcon({
  displayName: "AppleLogo",
  viewBox: "0 0 15 18",
  path: (
    <g width='15' height='18' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#Apple_logo_black 1__a)'>
        <path
          d='M12.507 17.28c-.966.947-2.031.8-3.046.353-1.08-.456-2.067-.484-3.207 0-1.42.619-2.173.439-3.029-.353C-1.602 12.258-.89 4.608 4.597 4.32c1.33.072 2.263.743 3.046.8 1.166-.24 2.28-.926 3.528-.836 1.498.122 2.619.72 3.367 1.795-3.082 1.872-2.352 5.976.479 7.128-.566 1.503-1.293 2.988-2.512 4.086l.002-.013ZM7.537 4.266C7.392 2.034 9.183.198 11.242.018c.283 2.574-2.316 4.5-3.705 4.248Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='Apple_logo_black 1__a'>
          <path fill='currentColor' d='M0 0h15v18H0z' />
        </clipPath>
      </defs>
    </g>
  ),
});
export const AndroidLogo = createIcon({
  displayName: "AndroidLogo",
  viewBox: "0 0 16 18",
  path: (
    <g width='16' height='18' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M4.9.051a.571.571 0 0 0-.263.066.518.518 0 0 0-.214.715l.595 1.029C3.584 2.71 2.634 4.16 2.634 5.815v.307a1.525 1.525 0 0 0-1.132-.494C.672 5.628 0 6.27 0 7.064v4.426c0 .793.673 1.437 1.502 1.437.452 0 .857-.192 1.132-.495v.62c0 .836.715 1.52 1.588 1.52h.404v1.969c0 .793.672 1.436 1.501 1.436.829 0 1.502-.643 1.502-1.436v-1.97h.697v1.97c0 .793.673 1.436 1.502 1.436.828 0 1.501-.643 1.501-1.436v-1.97h.403c.874 0 1.588-.683 1.588-1.519v-.62c.276.303.68.495 1.133.495.829 0 1.502-.644 1.502-1.437V7.064c0-.793-.673-1.436-1.502-1.436-.452 0-.857.191-1.133.494v-.307c0-1.655-.952-3.104-2.385-3.953l.596-1.03a.518.518 0 0 0-.214-.715.571.571 0 0 0-.748.205L9.945 1.4a5.906 5.906 0 0 0-1.968-.333c-.692 0-1.357.117-1.967.332L5.386.322a.556.556 0 0 0-.487-.27Z'
        fill='#fffcurrentColor'
      />
      <path
        d='M1.501 6.002c-.618 0-1.11.47-1.11 1.062v4.426c0 .592.492 1.063 1.11 1.063.618 0 1.11-.471 1.11-1.063V7.064c0-.591-.492-1.062-1.11-1.062Zm12.952 0c-.619 0-1.11.47-1.11 1.062v4.426c0 .592.491 1.063 1.11 1.063.618 0 1.11-.471 1.11-1.063V7.064c0-.591-.492-1.062-1.11-1.062Zm-8.327 5.05c-.618 0-1.11.471-1.11 1.063v4.426c0 .591.492 1.062 1.11 1.062.618 0 1.11-.47 1.11-1.062v-4.426c0-.592-.492-1.063-1.11-1.063Zm3.7 0c-.618 0-1.11.471-1.11 1.063v4.426c0 .591.492 1.062 1.11 1.062.619 0 1.111-.47 1.111-1.062v-4.426c0-.592-.492-1.063-1.11-1.063Z'
        fill='currentColor'
      />
      <path
        d='M4.9.426a.16.16 0 0 0-.074.02.141.141 0 0 0-.061.204l.782 1.352c-1.505.75-2.522 2.174-2.524 3.808h9.905c-.003-1.634-1.02-3.058-2.524-3.808L11.186.65c.044-.076.018-.163-.061-.205a.16.16 0 0 0-.075-.02.157.157 0 0 0-.14.079l-.792 1.367a5.483 5.483 0 0 0-2.143-.43c-.767 0-1.494.154-2.142.43L5.04.504A.157.157 0 0 0 4.9.426ZM3.024 6.184v6.868c0 .635.534 1.146 1.198 1.146h7.51c.663 0 1.197-.511 1.197-1.146V6.184H3.023Z'
        fill='currentColor'
      />
      <path
        d='M5.69 3.285a.407.407 0 0 0-.413.395c0 .216.187.395.414.395a.407.407 0 0 0 .413-.395.407.407 0 0 0-.413-.395Zm4.572 0a.407.407 0 0 0-.414.395c0 .216.188.395.414.395a.407.407 0 0 0 .413-.395.407.407 0 0 0-.413-.395Z'
        fill='currentColor'
      />
    </g>
  ),
});
export const WindowsLogo = createIcon({
  displayName: "WindowsLogo",
  viewBox: "0 0 19 18",
  path: (
    <g width='19' height='18' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='m0 2.537 7.705-.994.004 7.04-7.702.042L0 2.537Zm7.701 6.858.006 7.047L.006 15.44V9.348l7.695.047Zm.935-7.982L18.852 0v8.494l-10.216.077V1.413ZM18.854 9.46l-.002 8.456L8.636 16.55l-.015-7.106 10.233.016Z'
        fill='currentColor'
      />
    </g>
  ),
});

export const AtlassianLogo = createIcon({
  displayName: "AtlassianLogo",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        d='M6.427 9.239a.57.57 0 00-.798-.108.591.591 0 00-.167.209l-4.9 9.803a.586.586 0 00.524.847h6.827a.562.562 0 00.523-.324c1.474-3.043.58-7.67-2.01-10.427z'
        fill='#2684FF'
      />
      <path
        d='M10.028.318a12.932 12.932 0 00-.755 12.765l3.292 6.583a.586.586 0 00.523.324h6.826a.583.583 0 00.586-.585c0-.091-.02-.18-.06-.262L11.024.315a.552.552 0 00-.997 0v.003z'
        fill='#2684FF'
      />
    </g>
  ),
});

export const CartIcon = createIcon({
  displayName: "CartIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M7.984 19.937a1.406 1.406 0 100-2.812 1.406 1.406 0 000 2.812zM17.828 19.937a1.406 1.406 0 100-2.812 1.406 1.406 0 000 2.812zM20.324 5.558a1.051 1.051 0 00-.815-.386H6.134l-.27-1.528a.703.703 0 00-.692-.581H2.359a.703.703 0 000 1.406h2.223L6.59 15.841a.703.703 0 00.692.581h11.25a.703.703 0 100-1.406H7.871l-.248-1.406h10.62a1.057 1.057 0 001.035-.848l1.266-6.328a1.055 1.055 0 00-.22-.876z'
    />
  ),
});

export const ClockIcon = createIcon({
  displayName: "ClockIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <g>
        <rect fill='none' height='24' width='24' />
      </g>
      <g>
        <g>
          <g>
            <path
              fill='currentColor'
              d='M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10S17.5,2,12,2z M16.2,16.2L11,13V7h1.5v5.2l4.5,2.7L16.2,16.2z'
            />
          </g>
        </g>
      </g>
    </g>
  ),
});

export const CreditIcon = createIcon({
  displayName: "CreditIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M1.406 17.023a2.461 2.461 0 002.461 2.46h14.766a2.461 2.461 0 002.46-2.46v-6.768H1.407v6.768zm2.9-3.34a1.318 1.318 0 011.319-1.318h2.11a1.318 1.318 0 011.318 1.318v.879a1.318 1.318 0 01-1.319 1.318H5.625a1.318 1.318 0 01-1.318-1.318v-.879zM18.633 4.014H3.867a2.46 2.46 0 00-2.46 2.461v1.143h19.687V6.475a2.46 2.46 0 00-2.461-2.46z'
    />
  ),
});

export const DashboardLogo = createIcon({
  displayName: "DashboardLogo",
  viewBox: "0 0 1000 257",
  path: (
    <g width='998' height='257' viewBox='0 0 998 257' fill='none'>
      <g clipPath='url(#clip0)'>
        <path
          d='M388.5 115.302C406.112 115.302 413.966 126.726 416.584 136.96L441.336 127.916C436.576 109.352 419.44 89.836 388.262 89.836C354.942 89.836 329 115.54 329 151.24C329 186.464 354.942 212.882 388.976 212.882C419.44 212.882 436.814 193.128 442.288 175.04L418.012 166.234C415.394 175.04 407.064 187.654 388.976 187.654C371.602 187.654 356.608 174.564 356.608 151.24C356.608 127.916 371.602 115.302 388.5 115.302Z'
          fill='currentColor'
        />
        <path
          d='M484.894 141.244C485.37 126.488 493.7 115.064 508.932 115.064C526.306 115.064 532.732 126.488 532.732 140.768V209.312H560.34V136.008C560.34 110.542 546.536 90.074 517.976 90.074C505.838 90.074 492.748 94.358 484.894 104.592V37H457.286V209.312H484.894V141.244Z'
          fill='currentColor'
        />
        <path
          d='M577.29 177.896C577.29 196.222 592.284 212.882 616.56 212.882C635.362 212.882 646.786 203.362 652.26 194.556C652.26 203.838 653.212 208.598 653.45 209.312H679.154C678.916 208.122 677.726 201.22 677.726 190.748V133.152C677.726 110.066 664.16 89.836 628.46 89.836C599.9 89.836 581.812 107.686 579.67 127.678L604.898 133.39C606.088 121.728 614.18 112.446 628.698 112.446C643.93 112.446 650.594 120.3 650.594 130.058C650.594 134.104 648.69 137.436 642.026 138.388L612.276 142.91C592.522 145.766 577.29 157.19 577.29 177.896ZM621.796 191.224C610.848 191.224 604.898 184.084 604.898 176.23C604.898 166.71 611.8 161.95 620.368 160.522L650.594 156V161.236C650.594 183.846 637.266 191.224 621.796 191.224Z'
          fill='currentColor'
        />
        <path
          d='M810.108 93.406H773.456L728.95 140.292V37H701.58V209.312H728.95V176.944L743.23 161.95L777.264 209.312H811.06L762.508 142.434L810.108 93.406Z'
          fill='currentColor'
        />
        <path
          d='M889.349 92.692C888.159 92.454 885.303 91.978 881.971 91.978C866.739 91.978 853.887 99.356 848.413 111.97V93.406H821.519V209.312H849.127V154.096C849.127 132.438 858.885 120.062 880.305 120.062C883.161 120.062 886.255 120.3 889.349 120.776V92.692Z'
          fill='currentColor'
        />
        <path
          d='M895.968 177.896C895.968 196.222 910.962 212.882 935.238 212.882C954.04 212.882 965.464 203.362 970.938 194.556C970.938 203.838 971.89 208.598 972.128 209.312H997.832C997.594 208.122 996.404 201.22 996.404 190.748V133.152C996.404 110.066 982.838 89.836 947.138 89.836C918.578 89.836 900.49 107.686 898.348 127.678L923.576 133.39C924.766 121.728 932.858 112.446 947.376 112.446C962.608 112.446 969.272 120.3 969.272 130.058C969.272 134.104 967.368 137.436 960.704 138.388L930.954 142.91C911.2 145.766 895.968 157.19 895.968 177.896ZM940.474 191.224C929.526 191.224 923.576 184.084 923.576 176.23C923.576 166.71 930.478 161.95 939.046 160.522L969.272 156V161.236C969.272 183.846 955.944 191.224 940.474 191.224Z'
          fill='currentColor'
        />
        <rect width='257' height='257' rx='128.5' fill='#4FD1C5' />
        <path
          d='M69.5584 133.985L157.15 46.9959C158.787 45.3708 161.42 47.3484 160.315 49.3729L127.714 109.125C126.987 110.457 127.951 112.083 129.47 112.083H185.809C187.624 112.083 188.501 114.306 187.174 115.545L88.4456 207.687C86.6753 209.339 84.0405 207.011 85.4617 205.051L132.197 140.578C133.156 139.256 132.211 137.404 130.578 137.404H70.9677C69.1826 137.404 68.2917 135.243 69.5584 133.985Z'
          fill='white'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear'
          x1='128.5'
          y1='0'
          x2='128.5'
          y2='257'
          gradientUnits='userSpaceOnUse'>
          <stop stop-color='#7BCBD4' />
          <stop offset='1' stop-color='#29C6B7' />
        </linearGradient>
        <clipPath id='clip0'>
          <rect width='997.832' height='257' fill='white' />
        </clipPath>
      </defs>
    </g>
  ),
});

export const DashboardLogoWhite = createIcon({
  displayName: "DashboardLogo",
  viewBox: "0 0 163.5 42",
  path: (
    <g fill='none'>
      <path
        d='M63.452 19.292c2.877 0 4.16 1.911 4.587 3.623l4.043-1.513c-.777-3.106-3.576-6.371-8.668-6.371-5.443 0-9.68 4.3-9.68 10.274 0 5.893 4.237 10.313 9.796 10.313 4.976 0 7.813-3.305 8.707-6.331l-3.965-1.473c-.427 1.473-1.788 3.583-4.742 3.583-2.837 0-5.286-2.19-5.286-6.092 0-3.903 2.449-6.013 5.208-6.013zM79.196 23.632c.078-2.469 1.438-4.38 3.926-4.38 2.838 0 3.887 1.911 3.887 4.3v11.47h4.51V22.755c0-4.26-2.255-7.685-6.92-7.685-1.982 0-4.12.716-5.403 2.429V6.19h-4.509v28.831h4.51V23.632zM94.287 29.765c0 3.066 2.449 5.853 6.414 5.853 3.071 0 4.936-1.592 5.83-3.066 0 1.553.156 2.35.195 2.47h4.198c-.039-.2-.233-1.355-.233-3.107v-9.637c0-3.862-2.216-7.247-8.047-7.247-4.664 0-7.619 2.986-7.968 6.332l4.12.955c.194-1.951 1.516-3.504 3.887-3.504 2.488 0 3.576 1.314 3.576 2.947 0 .677-.311 1.234-1.399 1.393l-4.859.757c-3.226.478-5.714 2.39-5.714 5.854zm7.269 2.23c-1.788 0-2.76-1.195-2.76-2.509 0-1.593 1.127-2.39 2.527-2.628l4.936-.757v.876c0 3.783-2.176 5.018-4.703 5.018zM132.312 15.628h-5.986l-7.269 7.845V6.191h-4.47v28.83h4.47v-5.416l2.332-2.508 5.559 7.924h5.52l-7.93-11.19 7.774-8.203zM145.254 15.509a6.3 6.3 0 00-1.205-.12c-2.487 0-4.587 1.235-5.481 3.345v-3.106h-4.392v19.393h4.509v-9.238c0-3.624 1.594-5.695 5.092-5.695.467 0 .972.04 1.477.12v-4.7zM146.335 29.765c0 3.066 2.449 5.853 6.414 5.853 3.071 0 4.937-1.592 5.831-3.066 0 1.553.156 2.35.194 2.47h4.199c-.039-.2-.234-1.355-.234-3.107v-9.637c0-3.862-2.215-7.247-8.046-7.247-4.665 0-7.619 2.986-7.969 6.332l4.121.955c.194-1.951 1.516-3.504 3.887-3.504 2.488 0 3.576 1.314 3.576 2.947 0 .677-.311 1.234-1.399 1.393l-4.859.757c-3.227.478-5.715 2.39-5.715 5.854zm7.269 2.23c-1.788 0-2.759-1.195-2.759-2.509 0-1.593 1.127-2.39 2.526-2.628l4.937-.757v.876c0 3.783-2.177 5.018-4.704 5.018zM41.975 21.5C41.975 9.626 32.578 0 20.987 0 9.398 0 0 9.626 0 21.5S9.396 43 20.988 43c11.59 0 20.987-9.626 20.987-21.5z'
        fill='#fff'
      />
      <path
        d='M11.36 22.418L25.668 7.863c.267-.272.697.06.517.398l-5.325 9.997c-.119.223.039.495.287.495h9.202c.296 0 .44.372.223.58L14.446 34.748c-.29.277-.72-.113-.488-.44l7.633-10.788c.157-.221.003-.531-.264-.531H11.59c-.292 0-.437-.362-.23-.572z'
        fill='#3BCBBE'
      />
    </g>
  ),
});

export const DocumentIcon = createIcon({
  displayName: "DocumentIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        fill='currentColor'
        d='M18.809 10.344h-6.153a2.11 2.11 0 01-2.11-2.11V2.083a.176.176 0 00-.175-.176H6.328A2.812 2.812 0 003.516 4.72v14.063a2.812 2.812 0 002.812 2.812h9.844a2.812 2.812 0 002.812-2.813V10.52a.176.176 0 00-.175-.176z'
      />
      <path
        fill='currentColor'
        d='M18.423 8.789l-6.32-6.32a.088.088 0 00-.15.062v5.705a.703.703 0 00.703.703h5.705a.088.088 0 00.062-.15z'
      />
    </g>
  ),
});

export const GlobeIcon = createIcon({
  displayName: "GlobeIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        stroke='currentColor'
        fill='transparent'
        d='M11.25 2.109a9.14 9.14 0 100 18.281 9.14 9.14 0 000-18.281z'
        stroke-width='.75'
        stroke-miterlimit='10'
      />
      <path
        stroke='currentColor'
        fill='transparent'
        d='M11.25 2.109C8.698 2.109 6.3 6.2 6.3 11.249c0 5.048 2.4 9.14 4.951 9.14 2.552 0 4.951-4.092 4.951-9.14 0-5.048-2.399-9.14-4.95-9.14z'
        stroke-width='.75'
        stroke-miterlimit='10'
      />
      <path
        stroke='currentColor'
        fill='transparent'
        d='M5.156 5.156C6.836 6.349 8.952 7.06 11.25 7.06c2.298 0 4.414-.711 6.094-1.904'
      />
      <path
        stroke='currentColor'
        fill='transparent'
        d='M5.156 5.156C6.836 6.349 8.952 7.06 11.25 7.06c2.298 0 4.414-.711 6.094-1.904M17.344 17.344c-1.68-1.193-3.796-1.904-6.094-1.904-2.298 0-4.413.711-6.094 1.904'
        stroke-width='.75'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
      <path
        stroke='currentColor'
        fill='transparent'
        d='M11.25 2.109v18.28M20.39 11.249H2.11'
        stroke-width='.75'
        stroke-miterlimit='10'
      />
    </g>
  ),
});

export const HelpIcon = createIcon({
  displayName: "HelpIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M11.25 0C5.04 0 0 5.04 0 11.25S5.04 22.5 11.25 22.5 22.5 17.46 22.5 11.25 17.46 0 11.25 0zm-.352 17.813a1.172 1.172 0 110-2.344 1.172 1.172 0 010 2.344zm1.96-5.977c-.95.637-1.08 1.222-1.08 1.758a.82.82 0 11-1.641 0c0-1.284.59-2.305 1.806-3.121 1.13-.758 1.768-1.239 1.768-2.295 0-.718-.41-1.264-1.26-1.668-.199-.095-.644-.187-1.191-.18-.687.008-1.22.172-1.63.503-.775.623-.84 1.302-.84 1.312a.821.821 0 11-1.642-.08c.007-.142.106-1.425 1.452-2.507.698-.562 1.585-.854 2.636-.866.745-.01 1.444.117 1.918.34 1.418.672 2.198 1.79 2.198 3.146 0 1.982-1.325 2.872-2.494 3.658z'
    />
  ),
});

export const HomeIcon = createIcon({
  displayName: "HomeIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        fill='currentColor'
        d='M11.494 4.951a.351.351 0 00-.486 0l-8.09 7.729a.352.352 0 00-.109.254v7.254a1.406 1.406 0 001.405 1.406h4.223a.703.703 0 00.704-.703v-5.976a.351.351 0 01.351-.352h3.516a.351.351 0 01.351.352v5.976a.703.703 0 00.704.703h4.22a1.407 1.407 0 001.407-1.406v-7.254a.35.35 0 00-.108-.254L11.494 4.95z'
      />
      <path
        fill='currentColor'
        d='M21.574 11.23l-3.287-3.144V3.314a.703.703 0 00-.703-.703h-2.11a.703.703 0 00-.703.703V4.72l-2.545-2.434c-.239-.24-.593-.378-.976-.378-.38 0-.734.138-.972.379L.93 11.23a.717.717 0 00-.058.983.703.703 0 001.018.046l9.119-8.713a.352.352 0 01.486 0l9.12 8.713a.703.703 0 00.992-.019c.27-.28.248-.74-.033-1.01z'
      />
    </g>
  ),
});

export const InvisionLogo = createIcon({
  displayName: "InvisionLogo",
  viewBox: "0 0 24 24",
  path: (
    <g clipPath='url(#clip0)'>
      <path
        d='M18.687 0H2.313A1.813 1.813 0 00.5 1.811v16.374C.5 19.188 1.312 20 2.313 20h16.374a1.813 1.813 0 001.813-1.813V1.812A1.813 1.813 0 0018.687 0z'
        fill='#DC395F'
      />
      <path
        d='M7.184 6.293c.665 0 1.222-.522 1.222-1.204 0-.683-.557-1.204-1.222-1.204-.665 0-1.222.521-1.222 1.204 0 .682.557 1.204 1.222 1.204zM4.65 12.739a4.134 4.134 0 00-.108.905c0 1.06.575 1.764 1.797 1.764 1.013 0 1.834-.602 2.426-1.573l-.361 1.449h2.012l1.15-4.612c.287-1.168.844-1.774 1.689-1.774.665 0 1.078.413 1.078 1.096 0 .197-.018.413-.09.646l-.593 2.12c-.09.306-.126.611-.126.899 0 1.006.593 1.742 1.833 1.742 1.06 0 1.904-.682 2.371-2.317l-.79-.305c-.395 1.095-.737 1.293-1.006 1.293-.27 0-.414-.18-.414-.538 0-.162.037-.342.09-.558l.575-2.065c.144-.485.198-.915.198-1.31 0-1.546-.934-2.352-2.066-2.352-1.06 0-2.138.956-2.677 1.962l.395-1.806H8.962L8.53 8.996h1.438l-.885 3.544c-.695 1.545-1.972 1.57-2.132 1.534-.264-.06-.432-.159-.432-.5 0-.198.036-.482.126-.823l1.348-5.346H4.579l-.431 1.591h1.419L4.65 12.74'
        fill='#fff'
      />
    </g>
  ),
});

export const JiraLogo = createIcon({
  displayName: "JiraLogo",
  viewBox: "0 0 24 24",
  path: (
    <g clipPath='url(#clip0)'>
      <path
        d='M20.26 10.42l-8.863-8.93-.86-.865-6.67 6.722L.814 10.42a.827.827 0 000 1.162l6.096 6.14 3.627 3.654 6.67-6.722.105-.104 2.947-2.964a.824.824 0 000-1.166zm-9.722 3.649L7.493 11l3.045-3.068L13.582 11l-3.044 3.068z'
        fill='#2684FF'
      />
      <path
        d='M10.537 7.932a5.184 5.184 0 01-1.502-3.637A5.185 5.185 0 0110.515.65L3.852 7.36l3.626 3.654 3.06-3.082z'
        fill='url(#paint0_linear)'
      />
      <path
        d='M13.59 10.992l-3.053 3.076a5.186 5.186 0 011.502 3.653c0 1.37-.54 2.683-1.502 3.652l6.682-6.728-3.63-3.653z'
        fill='url(#paint1_linear)'
      />
    </g>
  ),
});

export const MastercardIcon = createIcon({
  displayName: "MastercardIcon",
  viewBox: "0 0 24 24",
  path: (
    <svg
      width='24'
      height='20'
      viewBox='0 0 21 15'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <circle cx='6.63158' cy='7.49999' r='6.63158' fill='#EB001B' />
      <circle cx='14.3686' cy='7.49999' r='6.63158' fill='#F79E1B' />
    </svg>
  ),
});

export const PayPalIcon = createIcon({
  displayName: "PayPalIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        fill='currentColor'
        d='M16.179 2.532C15.142 1.357 13.265.854 10.863.854H3.895a.997.997 0 00-.986.834L.007 19.945a.595.595 0 00.591.686H4.9l1.08-6.8-.033.215a.993.993 0 01.983-.834h2.044c4.017 0 7.16-1.619 8.08-6.3.027-.14.07-.406.07-.406.262-1.732-.001-2.907-.945-3.974z'
      />
      <path
        fill='currentColor'
        d='M18.213 7.393c-.998 4.61-4.184 7.048-9.24 7.048H7.142L5.773 23.1h2.973a.87.87 0 00.862-.731l.035-.184.684-4.297.044-.237a.87.87 0 01.86-.731h.544c3.514 0 6.264-1.416 7.068-5.51.322-1.644.166-3.021-.63-4.017z'
      />
    </g>
  ),
});

export const PersonIcon = createIcon({
  displayName: "PersonIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M14.618 3.338c-.855-.924-2.05-1.432-3.368-1.432-1.325 0-2.524.505-3.375 1.423-.86.928-1.28 2.188-1.181 3.55.195 2.686 2.239 4.87 4.556 4.87s4.358-2.184 4.556-4.87c.1-1.349-.322-2.607-1.188-3.541zM18.984 21.592H3.515a1.363 1.363 0 01-1.063-.489 1.552 1.552 0 01-.316-1.279c.371-2.058 1.529-3.786 3.348-5 1.616-1.076 3.664-1.67 5.766-1.67s4.15.594 5.765 1.67c1.82 1.214 2.977 2.942 3.348 5 .085.471-.03.937-.315 1.279a1.362 1.362 0 01-1.064.49z'
    />
  ),
});

export const ProfileIcon = createIcon({
  displayName: "ProfileIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path d='M0 0h24v24H0V0z' fill='transparent' />
      <path
        fill='currentColor'
        d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v1c0 .55.45 1 1 1h14c.55 0 1-.45 1-1v-1c0-2.66-5.33-4-8-4z'
      />
    </g>
  ),
});

export const RocketIcon = createIcon({
  displayName: "RocketIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        fill='currentColor'
        d='M20.99 2.182a.209.209 0 00-.156-.16c-2.574-.63-8.521 1.613-11.743 4.833a13.93 13.93 0 00-1.566 1.85c-.994-.087-1.987-.014-2.834.355-2.39 1.052-3.085 3.796-3.279 4.976a.424.424 0 00.464.492l3.837-.423c.002.29.02.578.052.866.02.2.108.386.25.527l1.486 1.482a.86.86 0 00.528.25c.286.033.573.05.86.053l-.42 3.832a.424.424 0 00.492.464c1.178-.19 3.927-.885 4.972-3.274.37-.847.445-1.836.36-2.824a13.96 13.96 0 001.855-1.567c3.232-3.216 5.462-9.03 4.842-11.732zm-8.067 7.896a2.11 2.11 0 112.983-2.984 2.11 2.11 0 01-2.983 2.984z'
      />
      <path
        fill='currentColor'
        d='M7.4 18.054c-.24.241-.627.335-1.092.416-1.044.178-1.967-.725-1.779-1.78.072-.401.283-.962.415-1.094a.192.192 0 00-.16-.328 2.636 2.636 0 00-1.544.753c-1.033 1.034-1.13 4.87-1.13 4.87s3.838-.097 4.872-1.13c.417-.417.682-.961.752-1.546.017-.184-.207-.293-.334-.16z'
      />
    </g>
  ),
});

export const SettingsIcon = createIcon({
  displayName: "SettingsIcon",
  viewBox: "0 0 24 24",
  // path can also be an array of elements, if you have multiple paths, lines, shapes, etc.
  path: (
    <g>
      <path d='M0,0h24v24H0V0z' fill='none' />
      <path
        fill='currentColor'
        d='M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z'
      />
    </g>
  ),
});

export const SlackLogo = createIcon({
  displayName: "SlackLogo",
  viewBox: "0 0 24 24",
  path: (
    <g clipPath='url(#clip0)' fill-rule='evenodd' clip-rule='evenodd'>
      <path
        d='M7.832.5c-1.105 0-2 .919-1.999 2.05 0 1.13.895 2.048 2 2.05h2V2.55c.001-1.13-.894-2.048-2-2.05zm0 5.467H2.5c-1.106 0-2.001.918-2 2.05-.002 1.13.894 2.048 2 2.05h5.332c1.106-.001 2.002-.919 2-2.05.002-1.132-.894-2.05-2-2.05z'
        fill='#36C5F0'
      />
      <path
        d='M20.5 8.016c0-1.13-.895-2.048-2-2.05-1.106.002-2.002.92-2 2.05v2.05h2c1.105 0 2-.918 2-2.05zm-5.334 0V2.55c.001-1.13-.893-2.048-2-2.05-1.105 0-2 .919-2 2.05v5.466c-.001 1.131.894 2.049 2 2.05 1.106 0 2.001-.918 2-2.05z'
        fill='#2EB67D'
      />
      <path
        d='M13.166 21c1.106 0 2.001-.919 2-2.05.001-1.13-.894-2.048-2-2.05h-2v2.05c-.001 1.13.894 2.048 2 2.05zm0-5.467h5.333c1.106-.001 2.002-.92 2-2.05.002-1.131-.893-2.049-1.999-2.05h-5.333c-1.106 0-2.001.918-2 2.05-.001 1.13.893 2.049 1.999 2.05z'
        fill='#ECB22E'
      />
      <path
        d='M.5 13.483c-.001 1.13.895 2.049 2 2.05 1.106-.001 2.001-.92 2-2.05v-2.05h-2c-1.105.001-2.001.919-2 2.05zm5.333 0v5.467c-.001 1.13.894 2.048 2 2.05 1.105 0 2-.919 2-2.05v-5.465c.002-1.131-.894-2.05-2-2.05-1.106 0-2 .917-2 2.048z'
        fill='#E01E5A'
      />
    </g>
  ),
});

export const SpotifyLogo = createIcon({
  displayName: "SpotifyLogo",
  viewBox: "0 0 24 24",
  path: (
    <g clipPath='url(#clip0)'>
      <path
        d='M10.5 0C4.977 0 .5 4.477.5 10s4.477 10 10 10 10-4.477 10-10c0-5.522-4.477-10-10-10zm4.586 14.422a.623.623 0 01-.857.208c-2.348-1.435-5.304-1.76-8.785-.964a.623.623 0 11-.277-1.216c3.808-.87 7.076-.495 9.712 1.115.294.181.387.564.207.857zm1.223-2.722a.78.78 0 01-1.072.257c-2.687-1.652-6.786-2.13-9.965-1.166a.78.78 0 01-.973-.519.781.781 0 01.52-.972c3.632-1.102 8.147-.569 11.233 1.329a.78.78 0 01.258 1.072zm.106-2.835C13.19 6.95 7.875 6.775 4.797 7.708a.935.935 0 11-.543-1.79c3.533-1.072 9.404-.865 13.115 1.338a.935.935 0 01-.954 1.609z'
        fill='#2EBD59'
      />
    </g>
  ),
});

export const HorizonLogo = createIcon({
  displayName: "horizonLogo",
  viewBox: "0 0 179 20",
  path: (
    <g width='179' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M16.42.748V19h-4.446v-7.514H5.058V19H.612V.748h4.446v7.15h6.916V.748h4.446Zm11.842 18.434c-1.716 0-3.294-.399-4.732-1.196a9.092 9.092 0 0 1-3.406-3.328c-.832-1.439-1.248-3.05-1.248-4.836 0-1.785.416-3.389 1.248-4.81a9.092 9.092 0 0 1 3.406-3.328C24.968.887 26.546.488 28.262.488c1.716 0 3.284.399 4.706 1.196a8.665 8.665 0 0 1 3.38 3.328c.832 1.421 1.248 3.025 1.248 4.81 0 1.785-.416 3.397-1.248 4.836a8.901 8.901 0 0 1-3.38 3.328c-1.422.797-2.99 1.196-4.706 1.196Zm0-4.056c1.456 0 2.617-.485 3.484-1.456.884-.97 1.326-2.253 1.326-3.848 0-1.612-.442-2.895-1.326-3.848-.867-.97-2.028-1.456-3.484-1.456-1.474 0-2.652.477-3.536 1.43-.867.953-1.3 2.245-1.3 3.874 0 1.612.433 2.903 1.3 3.874.884.953 2.062 1.43 3.536 1.43ZM49.377 19l-3.796-6.89h-1.066V19h-4.446V.748h7.462c1.439 0 2.66.251 3.666.754 1.023.503 1.785 1.196 2.288 2.08.503.867.754 1.837.754 2.912 0 1.213-.347 2.297-1.04 3.25-.676.953-1.681 1.63-3.016 2.028L54.395 19h-5.018ZM44.515 8.964h2.756c.815 0 1.421-.2 1.82-.598.416-.399.624-.962.624-1.69 0-.693-.208-1.24-.624-1.638-.399-.399-1.005-.598-1.82-.598h-2.756v4.524ZM61.476.748V19H57.03V.748h4.446ZM69.43 15.36h7.852V19H64.386v-3.38l7.8-11.232h-7.8V.748h12.896v3.38L69.43 15.36Zm19.388 3.822c-1.716 0-3.293-.399-4.732-1.196a9.093 9.093 0 0 1-3.406-3.328c-.832-1.439-1.248-3.05-1.248-4.836 0-1.785.416-3.389 1.248-4.81a9.093 9.093 0 0 1 3.406-3.328C85.525.887 87.102.488 88.818.488c1.716 0 3.285.399 4.706 1.196a8.664 8.664 0 0 1 3.38 3.328c.832 1.421 1.248 3.025 1.248 4.81 0 1.785-.416 3.397-1.248 4.836a8.9 8.9 0 0 1-3.38 3.328c-1.421.797-2.99 1.196-4.706 1.196Zm0-4.056c1.456 0 2.618-.485 3.484-1.456.884-.97 1.326-2.253 1.326-3.848 0-1.612-.442-2.895-1.326-3.848-.866-.97-2.028-1.456-3.484-1.456-1.473 0-2.652.477-3.536 1.43-.866.953-1.3 2.245-1.3 3.874 0 1.612.434 2.903 1.3 3.874.884.953 2.063 1.43 3.536 1.43ZM116.954 19h-4.446l-7.436-11.258V19h-4.446V.748h4.446l7.436 11.31V.748h4.446V19ZM136.32.878v1.924h-7.878v6.11h6.396v1.924h-6.396V19h-2.366V.878h10.244ZM148.72 19l-4.316-7.41h-2.86V19h-2.366V.878h5.85c1.369 0 2.522.234 3.458.702.953.468 1.664 1.1 2.132 1.898.468.797.702 1.707.702 2.73 0 1.248-.364 2.349-1.092 3.302-.711.953-1.786 1.586-3.224 1.898l4.55 7.592h-2.834Zm-7.176-9.308h3.484c1.282 0 2.244-.312 2.886-.936.641-.641.962-1.49.962-2.548 0-1.075-.321-1.907-.962-2.496-.624-.59-1.586-.884-2.886-.884h-3.484v6.864Zm15.818-6.89V8.86h6.604v1.95h-6.604v6.24h7.384V19h-9.75V.852h9.75v1.95h-7.384Zm13.33 0V8.86h6.604v1.95h-6.604v6.24h7.384V19h-9.75V.852h9.75v1.95h-7.384Z'
        fill='currentColor'
      />
    </g>
  ),
});

export const SupportIcon = createIcon({
  // Doesn't display the full icon without w and h being specified
  displayName: "BuildIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M20.885 5.547a.703.703 0 00-1.122-.176l-2.7 2.702a.708.708 0 01-.995 0l-1.167-1.169a.702.702 0 010-.994l2.689-2.69a.704.704 0 00-.21-1.138c-2.031-.908-4.566-.435-6.164 1.152-1.358 1.348-1.763 3.455-1.11 5.78a.698.698 0 01-.197.703L2.593 16.4a2.82 2.82 0 103.981 3.983l6.754-7.332a.699.699 0 01.693-.2 7.885 7.885 0 002.03.279c1.469 0 2.757-.475 3.686-1.39 1.72-1.695 1.983-4.57 1.148-6.192zM4.623 19.901a1.407 1.407 0 11-.305-2.797 1.407 1.407 0 01.305 2.797z'
    />
  ),
});

export const StatsIcon = createIcon({
  displayName: "StatsIcon",
  viewBox: "0 0 24 24",
  path: (
    <path
      fill='currentColor'
      d='M4.57 22.297H3.164a1.055 1.055 0 01-1.055-1.054v-6.328a1.055 1.055 0 011.055-1.055H4.57a1.055 1.055 0 011.055 1.055v6.328a1.055 1.055 0 01-1.055 1.054zM14.414 22.296h-1.406a1.055 1.055 0 01-1.055-1.055V10.695a1.055 1.055 0 011.055-1.055h1.406a1.055 1.055 0 011.055 1.055V21.24a1.055 1.055 0 01-1.055 1.055zM19.336 22.297H17.93a1.055 1.055 0 01-1.055-1.055V5.773A1.055 1.055 0 0117.93 4.72h1.406a1.055 1.055 0 011.055 1.054v15.47a1.055 1.055 0 01-1.055 1.054zM9.492 22.297H8.086a1.055 1.055 0 01-1.055-1.055V2.257a1.055 1.055 0 011.055-1.054h1.406a1.055 1.055 0 011.055 1.054v18.985a1.055 1.055 0 01-1.055 1.055z'
    />
  ),
});

export const WalletIcon = createIcon({
  displayName: "WalletIcon",
  viewBox: "0 0 24 24",
  path: (
    <g>
      <path
        fill='currentColor'
        d='M4.447 4.818h14.062c.164 0 .328.01.491.031a2.9 2.9 0 00-3.406-2.441L4.03 4.382h-.013a2.9 2.9 0 00-1.805 1.149 3.848 3.848 0 012.236-.713zM18.51 5.875H4.446a2.816 2.816 0 00-2.813 2.812v8.438a2.816 2.816 0 002.813 2.812h14.062a2.815 2.815 0 002.813-2.812V8.687a2.815 2.815 0 00-2.813-2.812zm-2.088 8.437a1.406 1.406 0 110-2.811 1.406 1.406 0 010 2.811z'
      />
      <path
        fill='currentColor'
        d='M1.656 11.651V7.28c0-.952.528-2.549 2.358-2.895 1.553-.291 3.091-.291 3.091-.291s1.011.703.176.703-.813 1.077 0 1.077 0 1.032 0 1.032L4.007 10.62l-2.35 1.032z'
      />
    </g>
  ),
});

export const VisaIcon = createIcon({
  displayName: "VisaIcon",
  viewBox: "0 0 24 24",
  path: (
    <svg
      width='26'
      height='20'
      viewBox='0 0 30 9'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M12.6708 2.96349C12.6568 4.15055 13.653 4.81298 14.4034 5.20684C15.1744 5.61099 15.4333 5.87013 15.4304 6.23147C15.4245 6.78459 14.8153 7.02862 14.2452 7.03812C13.2505 7.05475 12.6723 6.74889 12.2125 6.51753L11.8542 8.32341C12.3155 8.55247 13.1696 8.75217 14.0554 8.76087C16.1345 8.76087 17.4947 7.65543 17.5021 5.94145C17.5102 3.76625 14.7087 3.64579 14.7278 2.67348C14.7344 2.37871 14.9956 2.06408 15.568 1.98406C15.8512 1.94368 16.6332 1.91274 17.5198 2.35252L17.8677 0.605286C17.391 0.418253 16.7782 0.239136 16.0153 0.239136C14.0583 0.239136 12.6819 1.35962 12.6708 2.96349ZM21.2115 0.389687C20.8319 0.389687 20.5118 0.628245 20.3691 0.99433L17.3991 8.63249H19.4767L19.8901 7.40183H22.429L22.6689 8.63249H24.5L22.9021 0.389687H21.2115ZM21.5021 2.61641L22.1016 5.7116H20.4596L21.5021 2.61641ZM10.1518 0.389687L8.51418 8.63249H10.4939L12.1308 0.389687H10.1518ZM7.22303 0.389687L5.16233 6.00003L4.32878 1.22966C4.23097 0.697187 3.84472 0.389687 3.41579 0.389687H0.0471011L0 0.629037C0.691574 0.790671 1.47729 1.0514 1.95326 1.33033C2.24457 1.50067 2.32775 1.64964 2.42336 2.05458L4.00214 8.63249H6.0945L9.3021 0.389687H7.22303Z'
        fill='currentColor'
      />
    </svg>
  ),
});

export const BitcoinLogo = createIcon({
  displayName: "BitcoinLogo",
  viewBox: "0 0 67 14",
  path: (
    <svg width='67' height='14' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M13.779 8.694A6.993 6.993 0 1 1 .209 5.308a6.992 6.992 0 1 1 13.57 3.386Z'
        fill='#F7931A'
      />
      <path
        d='M10.078 6.004c.14-.932-.57-1.433-1.539-1.767l.315-1.261-.768-.192-.306 1.229c-.202-.05-.409-.098-.615-.145l.309-1.237-.768-.191-.314 1.26a25.67 25.67 0 0 1-.49-.115v-.004l-1.058-.264-.204.82s.57.13.558.139c.31.077.367.283.357.446L5.197 6.16c.021.005.05.013.08.025l-.081-.02-.502 2.014c-.038.094-.135.236-.352.182.008.011-.558-.14-.558-.14l-.38.88.998.249.547.141-.317 1.276.766.192.315-1.263c.21.057.412.11.611.16l-.313 1.256.767.191.318-1.274c1.309.248 2.293.148 2.707-1.036.334-.954-.016-1.504-.705-1.863.501-.116.88-.446.98-1.127Zm-1.754 2.46c-.237.954-1.842.438-2.362.31l.422-1.692c.52.13 2.188.388 1.94 1.382Zm.238-2.474c-.217.867-1.552.427-1.986.318l.383-1.533c.433.108 1.828.31 1.603 1.215Zm11.936-1.754c.568 0 1.058.102 1.47.302.413.201.755.473 1.029.815.27.343.471.744.6 1.204.13.462.195.958.195 1.489 0 .815-.15 1.586-.451 2.312a5.995 5.995 0 0 1-1.23 1.896 5.786 5.786 0 0 1-1.842 1.274 5.617 5.617 0 0 1-2.3.47c-.107 0-.295-.003-.56-.008a8.166 8.166 0 0 1-.91-.08 10.687 10.687 0 0 1-1.09-.213 5.458 5.458 0 0 1-1.089-.39L17.383.428l2.744-.425-1.097 4.57a4.19 4.19 0 0 1 .707-.247c.237-.06.49-.09.761-.09Zm-2.3 7.584c.412 0 .802-.101 1.167-.301.367-.2.684-.47.949-.806a4.07 4.07 0 0 0 .628-1.143 3.9 3.9 0 0 0 .23-1.33c0-.567-.094-1.009-.283-1.329-.189-.318-.538-.478-1.045-.478-.166 0-.38.032-.645.088a1.61 1.61 0 0 0-.718.373l-1.168 4.854c.07.012.133.024.186.035a1.918 1.918 0 0 0 .38.037h.318Zm8.439 1.932h-2.62l2.212-9.318h2.638l-2.23 9.317Zm1.275-10.454c-.366 0-.698-.109-.992-.327-.297-.217-.443-.551-.443-1.001 0-.248.05-.48.15-.7A1.87 1.87 0 0 1 27.6.323c.219-.094.452-.14.7-.14.366 0 .695.108.99.326.295.22.443.554.443 1.002 0 .248-.05.481-.15.7a1.844 1.844 0 0 1-.974.947c-.219.096-.45.141-.698.141Zm3.252-1.186 2.744-.425-.674 2.746h2.94l-.531 2.16H32.72l-.778 3.26a3.69 3.69 0 0 0-.124.761c-.013.237.017.44.088.612a.77.77 0 0 0 .38.398c.184.095.445.143.788.143.283 0 .558-.027.825-.08.264-.053.533-.126.804-.222l.196 2.02c-.355.13-.738.242-1.151.337a6.62 6.62 0 0 1-1.47.14c-.814 0-1.445-.12-1.895-.361-.449-.243-.766-.574-.956-.994-.187-.418-.271-.9-.248-1.442a9.351 9.351 0 0 1 .248-1.72l1.736-7.333Zm4.894 7.918c0-.802.13-1.559.39-2.267A5.58 5.58 0 0 1 37.563 5.9a5.215 5.215 0 0 1 1.762-1.257c.689-.306 1.46-.46 2.31-.46.531 0 1.006.05 1.424.15.42.102.8.234 1.143.399l-.904 2.056a8.454 8.454 0 0 0-.734-.257 3.194 3.194 0 0 0-.93-.116c-.874 0-1.566.302-2.08.903-.513.602-.77 1.412-.77 2.428 0 .602.129 1.09.39 1.462.259.372.738.558 1.433.558a4.841 4.841 0 0 0 1.842-.372l.196 2.108a9.48 9.48 0 0 1-1.098.347c-.402.098-.88.149-1.435.149-.732 0-1.352-.107-1.859-.319a3.54 3.54 0 0 1-1.256-.859 3.24 3.24 0 0 1-.717-1.268 5.205 5.205 0 0 1-.223-1.522Zm11.641 3.969c-.626 0-1.17-.096-1.63-.284a3.009 3.009 0 0 1-1.141-.797 3.443 3.443 0 0 1-.682-1.214 4.903 4.903 0 0 1-.229-1.548c0-.71.114-1.42.345-2.128a6.032 6.032 0 0 1 1.018-1.912 5.441 5.441 0 0 1 1.646-1.393c.648-.359 1.392-.54 2.23-.54.613 0 1.154.096 1.62.285a3.03 3.03 0 0 1 1.152.797c.299.343.527.746.68 1.214.154.465.23.983.23 1.55a7.06 7.06 0 0 1-.336 2.126 6.143 6.143 0 0 1-.991 1.913 5.198 5.198 0 0 1-1.637 1.39c-.655.36-1.414.54-2.275.54Zm1.31-7.582c-.39 0-.733.112-1.027.335a2.894 2.894 0 0 0-.743.851 4.202 4.202 0 0 0-.453 1.125 4.87 4.87 0 0 0-.149 1.16c0 .591.095 1.052.283 1.383.19.33.53.496 1.027.496.39 0 .731-.112 1.027-.337.293-.224.542-.508.743-.85.2-.342.352-.718.453-1.126.099-.406.15-.794.15-1.16 0-.59-.096-1.051-.285-1.382-.189-.33-.531-.495-1.027-.495Zm6.784 7.335h-2.622l2.212-9.318h2.64l-2.23 9.317Zm1.273-10.454c-.365 0-.697-.109-.99-.327-.296-.217-.444-.551-.444-1.001 0-.248.051-.48.151-.7.1-.218.233-.406.399-.566.164-.16.358-.286.575-.382.218-.094.45-.14.698-.14.367 0 .698.108.992.326.295.22.444.554.444 1.002 0 .248-.053.481-.152.7a1.837 1.837 0 0 1-.972.947 1.73 1.73 0 0 1-.7.141Zm2.845 1.559c.2-.058.422-.128.663-.204a9.764 9.764 0 0 1 1.824-.371c.372-.043.795-.063 1.265-.063 1.382 0 2.335.402 2.86 1.205.526.804.617 1.902.276 3.295l-1.205 5.032h-2.638l1.168-4.926c.072-.307.127-.604.169-.895a2.12 2.12 0 0 0-.01-.761.911.911 0 0 0-.326-.532c-.173-.136-.434-.204-.789-.204a5.32 5.32 0 0 0-1.044.108l-1.717 7.21h-2.639l2.143-8.894Z'
        fill='#fff'
      />
    </svg>
  ),
});

export const EthereumLogoOutline = createIcon({
  displayName: "EthereumLogoOutline",
  viewBox: "0 0 82 82",
  path: (
    <svg width='82' height='82' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M41 82c22.644 0 41-18.356 41-41S63.644 0 41 0 0 18.356 0 41s18.356 41 41 41Zm-.1-33.852-12.625-7.146 12.627 17.054L53.54 41.002 40.9 48.148Zm-12.436-9.44 12.632-20.082 12.628 20.088-12.63 7.147-12.63-7.154Z'
        fill='currentColor'
      />
    </svg>
  ),
});

export const DashCurveUp = createIcon({
  displayName: "DashCurveUp",
  viewBox: "0 0 134 22",
  path: (
    <g width='134' height='22' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M1 21v0C41.269-4.626 92.731-4.626 133 21v0'
        stroke='currentColor'
        stroke-width='2'
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-dasharray='6 6'
      />
    </g>
  ),
});
export const DashCurveDown = createIcon({
  displayName: "DashCurveDown",
  viewBox: "0 0 134 22",
  path: (
    <g width='134' height='22' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M1 1v0c40.269 25.626 91.731 25.626 132 0v0'
        stroke='currentColor'
        stroke-width='2'
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-dasharray='6 6'
      />
    </g>
  ),
});

export const ButtonLeft = createIcon({
  displayName: "ButtonLeft",
  viewBox: "0 0 44 29",
  path: (
    <g width='44' height='29' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M1 9.024A5 5 0 0 1 5.606 4.04l32-2.531A5 5 0 0 1 43 6.493v16.014a5 5 0 0 1-5.394 4.984l-32-2.53A5 5 0 0 1 1 19.975V9.024Z'
        stroke='currentColor'
        stroke-width='2'
      />
    </g>
  ),
});

export const ButtonRight = createIcon({
  displayName: "ButtonRight",
  viewBox: "0 0 44 29",
  path: (
    <svg width='44' height='29' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M43 9.024a5 5 0 0 0-4.606-4.984l-32-2.531A5 5 0 0 0 1 6.493v16.014a5 5 0 0 0 5.394 4.984l32-2.53A5 5 0 0 0 43 19.975V9.024Z'
        stroke='currentColor'
        stroke-width='2'
      />
    </svg>
  ),
});

export const Fingerprint = createIcon({
  displayName: "ButtonRight",
  viewBox: "0 0 48 48",
  path: (
    <g xmlns='http://www.w3.org/2000/svg' height='48' width='48'>
      <path
        fill='currentColor'
        d='M24.15 3.7Q27.45 3.7 30.625 4.5Q33.8 5.3 36.7 6.8Q37.15 7.05 37.225 7.425Q37.3 7.8 37.15 8.1Q36.95 8.4 36.6 8.575Q36.25 8.75 35.85 8.5Q33.2 7.1 30.2 6.375Q27.2 5.65 24.15 5.65Q21.05 5.65 18.1 6.35Q15.15 7.05 12.45 8.5Q11.95 8.75 11.6 8.65Q11.25 8.55 11.1 8.25Q10.9 8.05 10.975 7.6Q11.05 7.15 11.4 6.95Q14.3 5.3 17.575 4.5Q20.85 3.7 24.15 3.7ZM24.15 8.65Q29.5 8.65 34.25 10.975Q39 13.3 42.1 17.65Q42.4 18.1 42.325 18.475Q42.25 18.85 41.95 19.1Q41.65 19.35 41.25 19.325Q40.85 19.3 40.6 18.85Q37.8 14.9 33.425 12.775Q29.05 10.65 24.15 10.6Q19.2 10.6 14.875 12.725Q10.55 14.85 7.8 18.9Q7.45 19.35 7.05 19.425Q6.65 19.5 6.35 19.3Q6 19.15 5.925 18.775Q5.85 18.4 6 18.05Q9.05 13.6 13.85 11.125Q18.65 8.65 24.15 8.65ZM24.15 18.55Q28.8 18.55 32.125 21.725Q35.45 24.9 35.45 29.45Q35.45 29.95 35.225 30.275Q35 30.6 34.5 30.6Q33.95 30.6 33.7 30.275Q33.45 29.95 33.45 29.45Q33.45 25.65 30.725 23.05Q28 20.45 24.15 20.45Q20.3 20.45 17.6 23.15Q14.9 25.85 14.9 29.65Q14.9 33.65 16.15 36.625Q17.4 39.6 20.25 42.55Q20.6 42.9 20.6 43.25Q20.6 43.6 20.4 43.85Q20.15 44.2 19.625 44.175Q19.1 44.15 18.75 43.8Q15.9 40.8 14.425 37.35Q12.95 33.9 12.95 29.65Q12.95 25.05 16.2 21.8Q19.45 18.55 24.15 18.55ZM24.15 28.45Q24.6 28.45 24.85 28.75Q25.1 29.05 25.1 29.5Q25.1 33.9 27.975 36.2Q30.85 38.5 34.85 38.5Q35.35 38.5 36 38.4Q36.65 38.3 37.2 38.3Q37.6 38.3 37.875 38.55Q38.15 38.8 38.1 39.1Q38.1 39.5 37.925 39.725Q37.75 39.95 37.45 40.05Q36.75 40.3 36.025 40.35Q35.3 40.4 34.85 40.4Q30 40.4 26.6 37.575Q23.2 34.75 23.2 29.5Q23.2 29.05 23.45 28.75Q23.7 28.45 24.15 28.45ZM24.15 23.5Q26.7 23.5 28.5 25.225Q30.3 26.95 30.3 29.45Q30.3 31.2 31.525 32.4Q32.75 33.6 34.5 33.6Q36.2 33.6 37.4 32.4Q38.6 31.2 38.6 29.45Q38.6 23.6 34.35 19.625Q30.1 15.65 24.1 15.65Q18.1 15.65 13.9 19.725Q9.7 23.8 9.7 29.7Q9.7 31.55 9.975 33.35Q10.25 35.15 10.9 36.85Q11.05 37.35 10.925 37.65Q10.8 37.95 10.45 38.1Q10.05 38.25 9.65 38.15Q9.25 38.05 9.05 37.5Q8.55 36.05 8.175 34.025Q7.8 32 7.8 29.65Q7.8 23.1 12.625 18.325Q17.45 13.55 24.15 13.55Q30.95 13.55 35.75 18.175Q40.55 22.8 40.55 29.45Q40.55 31.95 38.8 33.75Q37.05 35.55 34.5 35.55Q31.9 35.55 30.1 33.825Q28.3 32.1 28.3 29.65Q28.3 27.9 27.1 26.65Q25.9 25.4 24.15 25.4Q22.45 25.4 21.225 26.65Q20 27.9 20 29.65Q20 34.75 22.9 38.225Q25.8 41.7 31.1 42.9Q31.55 42.95 31.725 43.275Q31.9 43.6 31.8 43.95Q31.75 44.35 31.45 44.55Q31.15 44.75 30.55 44.65Q24.7 43.45 21.375 39.375Q18.05 35.3 18.05 29.65Q18.05 27.1 19.825 25.3Q21.6 23.5 24.15 23.5Z'
      />
    </g>
  ),
});
