import { contactSchema } from './contactSchema.js';
import { propertySchema } from './propertySchema.js';
import { leadSchema } from './leadSchema.js';
import { userSchema } from './userSchema.js';
import { loginSchema } from './loginSchema.js';
import { emailSchema } from './emailSchema.js';
import { documentSchema } from './document.js';
import { phoneCallSchema } from './phoneCallSchema.js';
import { textMsgSchema } from './textMsgSchema.js';
import { TaskSchema } from './taskSchema.js';
import { MeetingSchema } from './meetingSchema.js';
import { addFiledSchema } from './addFiledSchema.js'

export {
    addFiledSchema,
    contactSchema,
    propertySchema,
    leadSchema,
    userSchema,
    loginSchema,
    emailSchema,
    documentSchema,
    TaskSchema,
    phoneCallSchema,
    textMsgSchema,
    MeetingSchema,
};